.rte-toolbar button {
    height: 36px;
    min-width: 36px;
}

.rte-editor-area {
    min-height: 300px;
    background: white;
    border: 1px solid #d1d5db;
    border-top: none;
}

.rte-code-mode {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    white-space: pre-wrap;
}

.rte-context-menu {
    position: fixed;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 150px;
}

.rte-context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
}

.rte-context-menu-item:hover {
    background: #f3f4f6;
}

.rte-context-menu-item:last-child {
    border-bottom: none;
}

.rte-context-menu-separator {
    height: 1px;
    background: #e5e7eb;
    margin: 4px 0;
}

.rte-table-tools {
    position: absolute;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 999;
    padding: 8px;
}

.rte-table-tools button {
    margin: 2px;
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    min-width: auto;
}

.rte-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rte-modal-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
}

table.rte-table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

table.rte-table td, table.rte-table th {
    border: 1px solid #d1d5db;
    padding: 8px;
    min-width: 50px;
    min-height: 30px;
}

.rte-table-selected {
    background: #dbeafe !important;
}

.rte-plugin-container {
    border-top: 1px solid #e5e7eb;
    padding: 10px;
    background: #f9fafb;
}

/* Основни стилове за контейнера */
.rte-container {
    font-family: 'Exo 2', sans-serif;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.rte-toolbar {
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
}

.rte-toolbar button {
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rte-toolbar button:hover {
    background: #e5e7eb;
    color: #111827;
}

.rte-toolbar button.active,
.rte-toolbar button.bg-blue-500 {
    background: #6e41b4 !important;
    color: white !important;
}

.rte-editor-area {
    outline: none;
    line-height: 1.6;
    font-size: 14px;
    color: #374151;
    padding: 16px;
    overflow-y: auto;
}

.rte-editor-area:focus {
    outline: none;
}

/* Скриване на оригиналния textarea */
.editor-container textarea {
    display: none !important;
}

/* Фокус стилове */
.rte-container:focus-within {
    border-color: #6e41b4;
    box-shadow: 0 0 0 3px rgba(110, 65, 180, 0.1);
}

/* Стилове за съдържанието в редактора */
.rte-editor-area p {
    margin: 0 0 16px 0;
}

.rte-editor-area p:last-child {
    margin-bottom: 0;
}

.rte-editor-area h1,
.rte-editor-area h2,
.rte-editor-area h3,
.rte-editor-area h4,
.rte-editor-area h5,
.rte-editor-area h6 {
    margin: 24px 0 16px 0;
    font-weight: 600;
    line-height: 1.3;
}

.rte-editor-area ul,
.rte-editor-area ol {
    margin: 16px 0;
    padding-left: 24px;
}

.rte-editor-area li {
    margin: 4px 0;
}

.rte-editor-area a {
    color: #6e41b4;
    text-decoration: underline;
}

.rte-editor-area img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 16px 0;
}

/* Inline input полета */
.rte-inline-input {
    position: absolute;
    z-index: 10001;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    padding: 12px;
    min-width: 300px;
    font-family: 'Exo 2', sans-serif;
}

.rte-inline-input-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rte-inline-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.rte-inline-input-row {
    display: flex;
    gap: 8px;
    align-items: center;
}

.rte-inline-field {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    color: #374151;
    outline: none;
    transition: border-color 0.2s ease;
}

.rte-inline-field:focus {
    border-color: #6e41b4;
    box-shadow: 0 0 0 3px rgba(110, 65, 180, 0.1);
}

.rte-inline-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 60px;
}

.rte-inline-btn-ok {
    background: #6e41b4;
    color: white;
}

.rte-inline-btn-ok:hover {
    background: #553c9a;
}

.rte-inline-btn-cancel {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.rte-inline-btn-cancel:hover {
    background: #e5e7eb;
}

/* Анимация за inline input */
.rte-inline-input {
    animation: slideInInput 0.2s ease-out;
}

@keyframes slideInInput {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}