.rte-toolbar button {
    height: 36px;
    min-width: 36px;
}

.rte-editor-area {
    min-height: 300px;
    background: white;
    border: 1px solid #d1d5db;
    border-top: none;
}

.rte-code-mode {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    white-space: pre-wrap;
}

.rte-context-menu {
    position: fixed;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 150px;
}

.rte-context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
}

.rte-context-menu-item:hover {
    background: #f3f4f6;
}

.rte-context-menu-item:last-child {
    border-bottom: none;
}

.rte-context-menu-separator {
    height: 1px;
    background: #e5e7eb;
    margin: 4px 0;
}

.rte-table-tools {
    position: absolute;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 999;
    padding: 8px;
}

.rte-table-tools button {
    margin: 2px;
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    min-width: auto;
}

.rte-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rte-modal-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
}

table.rte-table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

table.rte-table td, table.rte-table th {
    border: 1px solid #d1d5db;
    padding: 8px;
    min-width: 50px;
    min-height: 30px;
}

.rte-table-selected {
    background: #dbeafe !important;
}

.rte-plugin-container {
    border-top: 1px solid #e5e7eb;
    padding: 10px;
    background: #f9fafb;
}