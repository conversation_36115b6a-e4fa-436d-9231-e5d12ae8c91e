class AdvancedRichTextEditor {
    constructor(containerId, options = {}) {
        // Поддръжка за различни типове контейнери
        if (typeof containerId === 'string') {
            this.container = document.getElementById(containerId);
        } else if (containerId instanceof HTMLElement) {
            this.container = containerId;
        } else {
            throw new Error('Невалиден контейнер за редактора');
        }

        // Търсене на textarea в контейнера
        this.textarea = this.container.querySelector('textarea');

        this.options = {
            height: 300,
            width: '100%',
            plugins: [],
            toolbar: 'full',
            contextMenu: true,
            events: {},
            tableDefaults: {
                rows: 3,
                cols: 3,
                borderColor: '#d1d5db',
                backgroundColor: 'transparent'
            },
            autoSync: true, // Автоматична синхронизация с textarea
            ...options
        };

        this.isCodeMode = false;
        this.plugins = new Map();
        this.events = new Map();
        this.currentTable = null;
        this.contextMenu = null;
        this.tableTools = null;
        this.modal = null;

        this.init();
    }

    init() {
        this.createEditor();
        this.bindEvents();
        this.registerDefaultPlugins();
        this.triggerEvent('onInitialized', this);
    }

    createEditor() {
        // Скриване на оригиналния textarea ако съществува
        if (this.textarea) {
            this.textarea.style.display = 'none';
        }

        // Основен контейнер
        this.editorContainer = document.createElement('div');
        this.editorContainer.className = 'rte-container border rounded-lg overflow-hidden';

        // Toolbar
        this.toolbar = document.createElement('div');
        this.toolbar.className = 'rte-toolbar bg-gray-50 border-b p-2 flex items-center justify-between';

        // Лява част на toolbar-а
        const leftToolbar = document.createElement('div');
        leftToolbar.className = 'flex items-center space-x-1';

        // Дясна част на toolbar-а
        const rightToolbar = document.createElement('div');
        rightToolbar.className = 'flex items-center space-x-1';

        this.createToolbarButtons(leftToolbar, rightToolbar);

        this.toolbar.appendChild(leftToolbar);
        this.toolbar.appendChild(rightToolbar);

        // Редактираща област
        this.editorArea = document.createElement('div');
        this.editorArea.className = 'rte-editor-area p-4';
        this.editorArea.contentEditable = true;
        this.editorArea.style.height = this.options.height + 'px';

        // Зареждане на съдържание от textarea ако съществува
        if (this.textarea && this.textarea.value.trim()) {
            this.editorArea.innerHTML = this.textarea.value;
        } else {
            this.editorArea.innerHTML = '<p>Започнете да пишете тук...</p>';
        }

        // Контейнер за плъгини
        this.pluginContainer = document.createElement('div');
        this.pluginContainer.className = 'rte-plugin-container hidden';

        this.editorContainer.appendChild(this.toolbar);
        this.editorContainer.appendChild(this.editorArea);
        this.editorContainer.appendChild(this.pluginContainer);

        this.container.appendChild(this.editorContainer);
    }

    createToolbarButtons(leftContainer, rightContainer) {
        const leftButtons = [
            {icon: 'fas fa-bold', cmd: 'bold', title: 'Удебелен (Ctrl+B)'},
            {icon: 'fas fa-italic', cmd: 'italic', title: 'Курсив (Ctrl+I)'},
            {icon: 'fas fa-underline', cmd: 'underline', title: 'Подчертан (Ctrl+U)'},
            {icon: 'fas fa-strikethrough', cmd: 'strikethrough', title: 'Зачертан'},
            {separator: true},
            {icon: 'fas fa-list-ul', cmd: 'insertUnorderedList', title: 'Списък'},
            {icon: 'fas fa-list-ol', cmd: 'insertOrderedList', title: 'Номериран списък'},
            {separator: true},
            {icon: 'fas fa-align-left', cmd: 'justifyLeft', title: 'Ляво'},
            {icon: 'fas fa-align-center', cmd: 'justifyCenter', title: 'Център'},
            {icon: 'fas fa-align-right', cmd: 'justifyRight', title: 'Дясно'},
            {icon: 'fas fa-align-justify', cmd: 'justifyFull', title: 'Justify'},
            {separator: true},
            {icon: 'fas fa-link', cmd: 'createLink', title: 'Връзка'},
            {icon: 'fas fa-image', cmd: 'insertImage', title: 'Изображение'},
            {icon: 'fas fa-table', cmd: 'insertTable', title: 'Таблица'},
            {separator: true},
            {icon: 'fas fa-undo', cmd: 'undo', title: 'Отмени (Ctrl+Z)'},
            {icon: 'fas fa-redo', cmd: 'redo', title: 'Върни (Ctrl+Y)'}
        ];
        
        const rightButtons = [
            {icon: 'fas fa-code', cmd: 'toggleCode', title: 'CODE режим', id: 'code-btn'}
        ];
        
        this.createButtonGroup(leftButtons, leftContainer);
        this.createButtonGroup(rightButtons, rightContainer);
    }

    createButtonGroup(buttons, container) {
        buttons.forEach(btn => {
            if (btn.separator) {
                const separator = document.createElement('div');
                separator.className = 'w-px bg-gray-300 h-6 mx-1';
                container.appendChild(separator);
            } else {
                const button = document.createElement('button');
                button.className = 'px-3 py-2 text-gray-700 hover:bg-gray-200 rounded transition-colors flex items-center justify-center';
                button.innerHTML = `<i class="${btn.icon}"></i>`;
                button.title = btn.title;
                if (btn.id) button.id = btn.id;
                
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.executeCommand(btn.cmd);
                });
                
                container.appendChild(button);
            }
        });
    }

    executeCommand(cmd, value = null) {
        switch(cmd) {
            case 'toggleCode':
                this.toggleCodeMode();
                break;
            case 'createLink':
                this.insertLink();
                break;
            case 'insertImage':
                this.insertImage();
                break;
            case 'insertTable':
                this.insertTable();
                break;
            default:
                if (!this.isCodeMode) {
                    document.execCommand(cmd, false, value);
                    this.syncToTextarea();
                    this.triggerEvent('onChange', this.getContent(), this);
                }
                break;
        }
    }

    toggleCodeMode() {
        this.isCodeMode = !this.isCodeMode;
        const codeBtn = document.getElementById('code-btn');
        
        if (this.isCodeMode) {
            const html = this.editorArea.innerHTML;
            this.editorArea.textContent = this.formatHTML(html);
            this.editorArea.className = 'rte-editor-area rte-code-mode p-4';
            codeBtn.classList.add('bg-blue-500', 'text-white');
            this.triggerEvent('onCodeModeEnabled', this);
        } else {
            const text = this.editorArea.textContent;
            this.editorArea.innerHTML = text;
            this.editorArea.className = 'rte-editor-area p-4';
            codeBtn.classList.remove('bg-blue-500', 'text-white');
            this.triggerEvent('onCodeModeDisabled', this);
        }
        
        this.triggerEvent('onModeToggle', this.isCodeMode, this);
    }

    formatHTML(html) {
        return html.replace(/></g, '>\n<').replace(/^\s+|\s+$/g, '');
    }

    insertLink() {
        if (this.isCodeMode) return;
        
        const url = prompt('Въведете URL адрес:');
        if (url) {
            document.execCommand('createLink', false, url);
            this.syncToTextarea();
            this.triggerEvent('onLinkInsert', url, this);
            this.triggerEvent('onChange', this.getContent(), this);
        }
    }

    insertImage() {
        if (this.isCodeMode) return;
        
        const url = prompt('Въведете URL на изображението:');
        if (url) {
            document.execCommand('insertImage', false, url);
            this.syncToTextarea();
            this.triggerEvent('onImageInsert', url, this);
            this.triggerEvent('onChange', this.getContent(), this);
        }
    }

    insertTable() {
        if (this.isCodeMode) return;
        
        this.showTableModal();
    }

    showTableModal() {
        const modal = document.createElement('div');
        modal.className = 'rte-modal';
        
        modal.innerHTML = `
            <div class="rte-modal-content">
                <h3 class="text-lg font-semibold mb-4">Вмъкване на таблица</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-1">Редове:</label>
                        <input type="number" id="table-rows" value="${this.options.tableDefaults.rows}" min="1" max="20" class="w-full border rounded px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Колони:</label>
                        <input type="number" id="table-cols" value="${this.options.tableDefaults.cols}" min="1" max="10" class="w-full border rounded px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Цвят на рамката:</label>
                        <input type="color" id="table-border-color" value="#d1d5db" class="w-full h-10 border rounded">
                    </div>
                    <div class="flex space-x-2 justify-end">
                        <button onclick="this.closest('.rte-modal').remove()" class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400">Отказ</button>
                        <button onclick="window.editorInstance.createTable()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">Създай</button>
                    </div>
                </div>
            </div>
        `;
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });
        
        document.body.appendChild(modal);
        this.modal = modal;
        window.editorInstance = this; // Временна референция
    }

    createTable() {
        const rows = parseInt(document.getElementById('table-rows').value);
        const cols = parseInt(document.getElementById('table-cols').value);
        const borderColor = document.getElementById('table-border-color').value;
        
        let tableHTML = `<table class="rte-table" style="border-color: ${borderColor};">`;
        
        for (let i = 0; i < rows; i++) {
            tableHTML += '<tr>';
            for (let j = 0; j < cols; j++) {
                tableHTML += `<td style="border-color: ${borderColor};">Клетка ${i+1},${j+1}</td>`;
            }
            tableHTML += '</tr>';
        }
        tableHTML += '</table>';
        
        document.execCommand('insertHTML', false, tableHTML);

        this.modal.remove();
        this.syncToTextarea();
        this.triggerEvent('onTableInsert', tableHTML, this);
        this.triggerEvent('onChange', this.getContent(), this);
        
        // Добавяне на event listeners за таблични клетки
        setTimeout(() => {
            this.bindTableEvents();
        }, 100);
    }

    bindTableEvents() {
        const tables = this.editorArea.querySelectorAll('table.rte-table');
        tables.forEach(table => {
            table.addEventListener('click', (e) => {
                if (e.target.tagName === 'TD' || e.target.tagName === 'TH') {
                    this.selectTableCell(e.target);
                    this.showTableTools(e.target, e);
                }
            });
        });
    }

    selectTableCell(cell) {
        // Премахване на предишна селекция
        this.editorArea.querySelectorAll('.rte-table-selected').forEach(el => {
            el.classList.remove('rte-table-selected');
        });
        
        cell.classList.add('rte-table-selected');
        this.currentTable = cell.closest('table');
    }

    showTableTools(cell, event) {
        if (this.tableTools) {
            this.tableTools.remove();
        }
        
        const tools = document.createElement('div');
        tools.className = 'rte-table-tools';
        tools.innerHTML = `
            <div class="grid grid-cols-3 gap-1">
                <button onclick="window.editorInstance.addTableRow()" class="bg-green-500 text-white rounded px-2 py-1 text-xs">+ Ред</button>
                <button onclick="window.editorInstance.addTableCol()" class="bg-green-500 text-white rounded px-2 py-1 text-xs">+ Колона</button>
                <button onclick="window.editorInstance.deleteTableRow()" class="bg-red-500 text-white rounded px-2 py-1 text-xs">- Ред</button>
                <button onclick="window.editorInstance.deleteTableCol()" class="bg-red-500 text-white rounded px-2 py-1 text-xs">- Колона</button>
                <button onclick="window.editorInstance.changeCellColor()" class="bg-blue-500 text-white rounded px-2 py-1 text-xs">Цвят</button>
                <button onclick="window.editorInstance.deleteTable()" class="bg-gray-500 text-white rounded px-2 py-1 text-xs">Изтрий</button>
            </div>
        `;
        
        tools.style.left = event.pageX + 'px';
        tools.style.top = event.pageY + 'px';
        
        document.body.appendChild(tools);
        this.tableTools = tools;
        
        // Автоматично скриване след 5 секунди
        setTimeout(() => {
            if (this.tableTools) {
                this.tableTools.remove();
                this.tableTools = null;
            }
        }, 5000);
    }

    addTableRow() {
        if (!this.currentTable) return;
        
        const selectedCell = this.editorArea.querySelector('.rte-table-selected');
        const row = selectedCell.closest('tr');
        const newRow = row.cloneNode(true);
        
        // Изчистване на съдържанието
        newRow.querySelectorAll('td, th').forEach(cell => {
            cell.textContent = 'Нова клетка';
            cell.classList.remove('rte-table-selected');
        });
        
        row.parentNode.insertBefore(newRow, row.nextSibling);
        this.triggerEvent('onTableRowAdd', newRow, this);
        this.triggerEvent('onChange', this.getContent(), this);
    }

    addTableCol() {
        if (!this.currentTable) return;
        
        const selectedCell = this.editorArea.querySelector('.rte-table-selected');
        const cellIndex = Array.from(selectedCell.parentNode.children).indexOf(selectedCell);
        
        this.currentTable.querySelectorAll('tr').forEach(row => {
            const newCell = document.createElement(row.querySelector('th') ? 'th' : 'td');
            newCell.textContent = 'Нова клетка';
            newCell.style.border = selectedCell.style.border || '1px solid #d1d5db';
            
            if (cellIndex < row.children.length - 1) {
                row.insertBefore(newCell, row.children[cellIndex + 1]);
            } else {
                row.appendChild(newCell);
            }
        });
        
        this.triggerEvent('onTableColAdd', cellIndex, this);
        this.triggerEvent('onChange', this.getContent(), this);
    }

    deleteTableRow() {
        if (!this.currentTable) return;
        
        const selectedCell = this.editorArea.querySelector('.rte-table-selected');
        const row = selectedCell.closest('tr');
        
        if (this.currentTable.querySelectorAll('tr').length > 1) {
            row.remove();
            this.triggerEvent('onTableRowDelete', row, this);
            this.triggerEvent('onChange', this.getContent(), this);
        }
    }

    deleteTableCol() {
        if (!this.currentTable) return;
        
        const selectedCell = this.editorArea.querySelector('.rte-table-selected');
        const cellIndex = Array.from(selectedCell.parentNode.children).indexOf(selectedCell);
        
        const firstRow = this.currentTable.querySelector('tr');
        if (firstRow.children.length > 1) {
            this.currentTable.querySelectorAll('tr').forEach(row => {
                if (row.children[cellIndex]) {
                    row.children[cellIndex].remove();
                }
            });
            
            this.triggerEvent('onTableColDelete', cellIndex, this);
            this.triggerEvent('onChange', this.getContent(), this);
        }
    }

    changeCellColor() {
        const selectedCell = this.editorArea.querySelector('.rte-table-selected');
        if (!selectedCell) return;
        
        const color = prompt('Въведете цвят (hex код, например #ff0000):');
        if (color) {
            selectedCell.style.backgroundColor = color;
            this.triggerEvent('onTableCellStyle', {cell: selectedCell, property: 'backgroundColor', value: color}, this);
            this.triggerEvent('onChange', this.getContent(), this);
        }
    }

    deleteTable() {
        if (this.currentTable) {
            this.currentTable.remove();
            this.triggerEvent('onTableDelete', this.currentTable, this);
            this.triggerEvent('onChange', this.getContent(), this);
            this.currentTable = null;
        }
        
        if (this.tableTools) {
            this.tableTools.remove();
            this.tableTools = null;
        }
    }

    createContextMenu(event) {
        if (this.contextMenu) {
            this.contextMenu.remove();
        }
        
        const selection = window.getSelection();
        const hasSelection = selection.toString().length > 0;
        const target = event.target;
        const isTable = target.closest('table');
        const isLink = target.closest('a');
        const isImage = target.tagName === 'IMG';
        
        const menuItems = [];
        
        if (hasSelection) {
            menuItems.push(
                {text: 'Копирай', action: () => document.execCommand('copy')},
                {text: 'Изрежи', action: () => document.execCommand('cut')},
                {separator: true},
                {text: 'Удебели', action: () => this.executeCommand('bold')},
                {text: 'Курсив', action: () => this.executeCommand('italic')},
                {text: 'Подчертай', action: () => this.executeCommand('underline')}
            );
        } else {
            menuItems.push({text: 'Постави', action: () => document.execCommand('paste')});
        }
        
        if (isTable) {
            menuItems.push(
                {separator: true},
                {text: 'Таблица - Добави ред', action: () => this.addTableRow()},
                {text: 'Таблица - Добави колона', action: () => this.addTableCol()},
                {text: 'Таблица - Изтрий', action: () => this.deleteTable()}
            );
        }
        
        if (isLink) {
            menuItems.push(
                {separator: true},
                {text: 'Редактирай връзка', action: () => this.editLink(target.closest('a'))},
                {text: 'Премахни връзка', action: () => this.removeLink(target.closest('a'))}
            );
        }
        
        if (isImage) {
            menuItems.push(
                {separator: true},
                {text: 'Редактирай изображение', action: () => this.editImage(target)},
                {text: 'Премахни изображение', action: () => target.remove()}
            );
        }
        
        menuItems.push(
            {separator: true},
            {text: 'Вмъкни връзка', action: () => this.insertLink()},
            {text: 'Вмъкни изображение', action: () => this.insertImage()},
            {text: 'Вмъкни таблица', action: () => this.insertTable()}
        );
        
        this.showContextMenu(menuItems, event.pageX, event.pageY);
    }

    showContextMenu(items, x, y) {
        const menu = document.createElement('div');
        menu.className = 'rte-context-menu';
        menu.style.left = x + 'px';
        menu.style.top = y + 'px';
        
        items.forEach(item => {
            if (item.separator) {
                const separator = document.createElement('div');
                separator.className = 'rte-context-menu-separator';
                menu.appendChild(separator);
            } else {
                const menuItem = document.createElement('div');
                menuItem.className = 'rte-context-menu-item';
                menuItem.textContent = item.text;
                menuItem.addEventListener('click', () => {
                    item.action();
                    menu.remove();
                });
                menu.appendChild(menuItem);
            }
        });
        
        document.body.appendChild(menu);
        this.contextMenu = menu;
        
        // Затваряне при клик извън менюто
        setTimeout(() => {
            document.addEventListener('click', () => {
                if (menu.parentNode) {
                    menu.remove();
                }
            }, {once: true});
        }, 100);
    }

    bindEvents() {
        // Клавиатурни комбинации
        this.editorArea.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key.toLowerCase()) {
                    case 'b':
                        e.preventDefault();
                        this.executeCommand('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        this.executeCommand('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        this.executeCommand('underline');
                        break;
                    case 'z':
                        e.preventDefault();
                        this.executeCommand('undo');
                        break;
                    case 'y':
                        e.preventDefault();
                        this.executeCommand('redo');
                        break;
                }
            }
        });
        
        // Контекстно меню
        if (this.options.contextMenu) {
            this.editorArea.addEventListener('contextmenu', (e) => {
                if (!this.isCodeMode) {
                    e.preventDefault();
                    this.createContextMenu(e);
                }
            });
        }
        
        // Промени в съдържанието
        this.editorArea.addEventListener('input', () => {
            this.syncToTextarea();
            this.triggerEvent('onChange', this.getContent(), this);
        });

        // Focus и blur събития
        this.editorArea.addEventListener('focus', () => {
            this.triggerEvent('onFocus', this);
        });

        this.editorArea.addEventListener('blur', () => {
            this.syncToTextarea();
            this.triggerEvent('onBlur', this);
        });
        
        // Resize observer за промени в размера
        if (window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(() => {
                this.triggerEvent('onResize', this.editorArea.offsetWidth, this.editorArea.offsetHeight, this);
            });
            resizeObserver.observe(this.editorArea);
        }
        
        // Клик извън редактора за затваряне на менюта
        document.addEventListener('click', (e) => {
            if (!this.editorContainer.contains(e.target)) {
                if (this.contextMenu) {
                    this.contextMenu.remove();
                    this.contextMenu = null;
                }
                if (this.tableTools) {
                    this.tableTools.remove();
                    this.tableTools = null;
                }
            }
        });
    }

    // Система за събития
    on(eventName, callback) {
        if (!this.events.has(eventName)) {
            this.events.set(eventName, []);
        }
        this.events.get(eventName).push(callback);
    }

    off(eventName, callback) {
        if (this.events.has(eventName)) {
            const callbacks = this.events.get(eventName);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    triggerEvent(eventName, ...args) {
        // Вградени събития от опциите
        if (this.options.events && this.options.events[eventName]) {
            this.options.events[eventName](...args);
        }
        
        // Регистрирани събития
        if (this.events.has(eventName)) {
            this.events.get(eventName).forEach(callback => {
                callback(...args);
            });
        }
    }

    // Система за плъгини
    registerPlugin(name, plugin) {
        this.plugins.set(name, plugin);
        if (plugin.init && typeof plugin.init === 'function') {
            plugin.init(this);
        }
        this.triggerEvent('onPluginRegistered', name, plugin, this);
    }

    unregisterPlugin(name) {
        const plugin = this.plugins.get(name);
        if (plugin && plugin.destroy && typeof plugin.destroy === 'function') {
            plugin.destroy(this);
        }
        this.plugins.delete(name);
        this.triggerEvent('onPluginUnregistered', name, this);
    }

    getPlugin(name) {
        return this.plugins.get(name);
    }

    registerDefaultPlugins() {
        // Примерен плъгин за автозапазване
        this.registerPlugin('autosave', {
            init: (editor) => {
                this.autosaveInterval = setInterval(() => {
                    const content = editor.getContent();
                    localStorage.setItem('rte_autosave', content);
                    editor.triggerEvent('onAutosave', content, editor);
                }, 30000); // Всеки 30 секунди
            },
            destroy: () => {
                if (this.autosaveInterval) {
                    clearInterval(this.autosaveInterval);
                }
            }
        });
        
        // Плъгин за статистика
        this.registerPlugin('statistics', {
            init: (editor) => {
                editor.on('onChange', (content) => {
                    const stats = this.calculateStats(content);
                    editor.triggerEvent('onStatsUpdate', stats, editor);
                });
            },
            calculateStats: (content) => {
                const text = content.replace(/<[^>]*>/g, '');
                return {
                    characters: text.length,
                    words: text.split(/\s+/).filter(word => word.length > 0).length,
                    paragraphs: (content.match(/<p[^>]*>/g) || []).length
                };
            }
        });
    }

    calculateStats(content) {
        const text = content.replace(/<[^>]*>/g, '');
        return {
            characters: text.length,
            words: text.split(/\s+/).filter(word => word.length > 0).length,
            paragraphs: (content.match(/<p[^>]*>/g) || []).length
        };
    }

    // Основни методи
    getContent() {
        if (this.isCodeMode) {
            return this.editorArea.textContent;
        } else {
            return this.editorArea.innerHTML;
        }
    }

    setContent(content) {
        if (this.isCodeMode) {
            this.editorArea.textContent = content;
        } else {
            this.editorArea.innerHTML = content;
        }
        this.syncToTextarea();
        this.triggerEvent('onContentSet', content, this);
        this.triggerEvent('onChange', content, this);
    }

    // Синхронизация с textarea
    syncToTextarea() {
        if (this.textarea && this.options.autoSync) {
            this.textarea.value = this.getContent();
            // Задействане на change event за textarea
            const event = new Event('change', { bubbles: true });
            this.textarea.dispatchEvent(event);
        }
    }

    // Синхронизация от textarea към редактора
    syncFromTextarea() {
        if (this.textarea) {
            const content = this.textarea.value;
            if (content !== this.getContent()) {
                this.setContent(content);
            }
        }
    }

    focus() {
        this.editorArea.focus();
    }

    blur() {
        this.editorArea.blur();
    }

    isEmpty() {
        const content = this.getContent().trim();
        return content === '' || content === '<p><br></p>' || content === '<p></p>';
    }

    destroy() {
        this.triggerEvent('onDestroy', this);
        
        // Унищожаване на плъгини
        this.plugins.forEach((plugin, name) => {
            this.unregisterPlugin(name);
        });
        
        // Премахване на event listeners и DOM елементи
        if (this.contextMenu) this.contextMenu.remove();
        if (this.tableTools) this.tableTools.remove();
        if (this.modal) this.modal.remove();
        
        this.editorContainer.remove();
    }

    // Статични методи за автоматична инициализация
    static initializeAll(selector = '.editor-container', options = {}) {
        const containers = document.querySelectorAll(selector);
        const editors = [];

        containers.forEach(container => {
            try {
                const editor = new AdvancedRichTextEditor(container, options);
                editors.push(editor);
            } catch (error) {
                console.error('Грешка при инициализация на редактор:', error);
            }
        });

        return editors;
    }

    static initialize(containerId, options = {}) {
        return new AdvancedRichTextEditor(containerId, options);
    }
}